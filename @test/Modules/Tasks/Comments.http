### Variables
@baseUrl = https://localhost:7001
@taskId = 550e8400-e29b-41d4-a716-446655440000

### List Comments - Default Sorting (InsertDate desc)
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments
Authorization: Bearer {{token}}

### List Comments - Sort by InsertDate ascending
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments?sortProperty=InsertDate&sortType=asc
Authorization: Bearer {{token}}

### List Comments - Sort by Content descending
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments?sortProperty=Content&sortType=desc
Authorization: Bearer {{token}}

### List Comments - Sort by UserId ascending
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments?sortProperty=UserId&sortType=asc
Authorization: Bearer {{token}}

### List Comments - Invalid sort property (should fallback to default)
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments?sortProperty=InvalidProperty&sortType=desc
Authorization: Bearer {{token}}

### List Comments - Invalid sort type (should fallback to desc)
GET {{baseUrl}}/api/v1/tasks/tasks/{{taskId}}/comments?sortProperty=InsertDate&sortType=invalid
Authorization: Bearer {{token}}
