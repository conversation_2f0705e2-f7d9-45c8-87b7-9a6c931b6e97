import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { SendOutlined } from "@ant-design/icons";
import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { sendMessage } from "../../Services";


const SendMessage = () => {
  const [form] = Form.useForm();
  const {selectedChatItem} = useSelector((state: RootState) => state.chat);
  const {userInfoes} = useSelector((state:RootState)=>state.profile)
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    formValues["ChatId"] = selectedChatItem?.Id
    formValues["Direction"] = 2
    formValues["SenderId"]=userInfoes?.Id

    formValues["ContentType"]=1

    try {
      form.resetFields();
      await sendMessage(formValues)
      queryClient.resetQueries({
        queryKey: endPoints.getChatMessageDetails,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };
  return (
    <MazakaForm
      form={form}
      onFinish={handleOnFinish}
      submitButtonVisible={false}
    >
      <MazakaTextArea
        autoSize
        label=" "
        name="Content"
        rules={[{required:true,message:""}]}
        size="large"
        placeholder="Say Hello"
        className="!w-[72%] !border-t !border-t-[#d9d9d9] !border-l-0 !border-r-0 !border-b-0 rounded-none shadow-none focus:shadow-none"
      />

      <MazakaButton
      htmlType="submit"
      icon={<SendOutlined />} status="save">
        Sned
      </MazakaButton>
    </MazakaForm>
  );
};

export default SendMessage;
