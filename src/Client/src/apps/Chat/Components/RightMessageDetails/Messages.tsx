import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { useGetChatMessageDetails } from "../../ServerSideStates";




const Messages = () => {
  const {selectedChatItem} = useSelector((state: RootState) => state.chat);
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);

  console.log("chat details",chatDetails)
  

  return (
    <div className="flex-1 overflow-auto space-y-4">
      {chatDetails?.data?.Value?.Messages?.map((msg: any, index: number) => (
        
        <div
          key={index}
          className={`flex items-start space-x-3 ${msg.Direction!=="Incoming" ? "justify-end" : "justify-start"}`}
        >
          
          <div
            className={`p-3 rounded-lg max-w-xs flex flex-col gap-1 ${
             msg.Direction!=="Incoming"
                ? "bg-gray-200 text-gray-900"
                : "bg-yellow-100 text-gray-900"
            }`}
          >
            <p className="text-sm">{msg.Content}</p>

            <p className="flex items-center justify-between gap-2">
              <span className="text-xs text-gray-500 mt-1 flex items-center gap-2">
                <span>{dayjs(msg.SentAt).format("DD.MM.YYYY")}</span>
                <span>{dayjs(msg.SentAt).format("HH:mm")}</span>
              </span>
              {/* {msg?.isSaved ? (
                <CheckOutlined className="text-xs text-green-500" />
              ) : (
                <ClockCircleOutlined className="text-xs text-[#87947c]" />
              )} */}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Messages;
