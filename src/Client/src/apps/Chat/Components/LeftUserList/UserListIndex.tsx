import { Col, List, Row } from "antd";
import UserListItem from "./UserListItem";
import { useGetUserChats } from "../../ServerSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { hanldleSetUserChatFilter } from "../../ClientSideStates";
import TabStatus from "./TabStatus";



const UserListIndex = () => {
  const {userChatFilter:filter} = useSelector((state:RootState)=>state.chat)
  const userChats = useGetUserChats(filter)
  const dispatch = useDispatch()

  const handleChangePagination = (pageNum: number, pageSize: number) => {
      let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
      dispatch(hanldleSetUserChatFilter({ filter: newFilter }));
    };


  return (
    <Row className=" !border-r !border-gray-300 !h-screen ">
      <Col xs={24} >
      <TabStatus/>
      </Col>
      <Col xs={24} className="!h-[100%]" >
      <List
        className=" "
        loading={userChats.isLoading||userChats.isFetching}
        dataSource={userChats?.data?.Value||[]}
        pagination={{
          className:"!flex !justify-center md:!justify-end !p-4",
           onChange: handleChangePagination,
           total: userChats.data?.FilteredCount||0,
           current: userChats.data?.PageNumber,
           pageSize: userChats.data?.PageSize,
           showLessItems: true,
           size: "small",
           showSizeChanger: true,
           locale: { items_per_page: "" },
           showTotal: (e) => `${e}`,
         }}
        renderItem={(item:any) => (
        
          <List.Item className="!px-0 !py-0 !w-full ">
            <UserListItem item={item} />
          </List.Item>
      
        )}
      />
      </Col>
    </Row>
  );
};

export default UserListIndex;
