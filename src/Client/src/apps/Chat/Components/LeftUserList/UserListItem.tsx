import { EllipsisOutlined, WhatsAppOutlined } from "@ant-design/icons";
import { Typography, Image, Avatar, Dropdown } from "antd";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import ChatItemOptions from "./ChatItemOptions";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetSelectedUserListChatItem } from "../../ClientSideStates";
import { RootState } from "@/store/Reducers";

const { Text } = Typography;

const UserListItem = ({ item }: any) => {
  const {selectedChatItem} = useSelector((state: RootState) => state.chat);
  let name = item?.Customer?.Name || item?.FullName || "servis";
  const dispatch = useDispatch();
  const initials = name
    .split(" ")
    .map((n: any) => n[0])
    .join("")
    .toUpperCase();

  const { t } = useTranslation();

  const lastMessage = item.Messages.reduce((latest: any, current: any) => {
    return dayjs(current.SentAt).isAfter(dayjs(latest.SentAt))
      ? current
      : latest;
  });

  return (
    <>
      <div
        className="!flex hover:cursor-pointer !w-full"
        onClick={() => {
          dispatch(hanldleSetSelectedUserListChatItem({ data: item }));
        }}
      >
        <div className="!w-[60px] !h-[50px] !bg-[#58666e] !flex items-center justify-center !cursor-pointer !relative">
          <Text className="!text-lg !text-white">{initials}</Text>

          <span className={`  absolute bottom-0 left-0  `}>
            <div className="!flex items-end !text-white">
              <WhatsAppOutlined className="!text-[#21c55e]" />
            </div>
          </span>
        </div>

        <div className="flex flex-col gap-1 w-full p-1">
          <div className="flex justify-between items-center">
            <div className="flex gap-1 items-center">
              <ExpandableText
                title={t("customers.add.address")}
                limit={18}
                text={name}
                textClassName="!text-xs !text-black"
              />
            </div>
            <Text className="text-[10px] !text-black">
              {dayjs(item.StartedAt).format("DD.MM.YYYY HH:mm")}
            </Text>
          </div>
          <div className="!flex justify-between items-center !w-full">
            <ExpandableText
              title={t("customers.add.address")}
              limit={30}
              text={lastMessage?.Content || ""}
              textClassName="!text-[11px] !text-gray-600"
            />
            <ChatItemOptions item={item} />
          </div>
        </div>
      </div>
    </>
  );
};

export default UserListItem;
